--liquibase formatted sql

--changeset xbxu1:
-- 1）贸易国别（长度 60 × 2 = 120）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "TRADE_COUNTRY" VARCHAR(120);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."TRADE_COUNTRY" IS '贸易国别';

-- 2）装运人 SHIPPER（长度 300 × 2 = 600）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "SHIPPER" VARCHAR(600);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."SHIPPER" IS '装运人';

-- 3）收货人 CONSIGNEE（长度 300 × 2 = 600）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "CONSIGNEE" VARCHAR(600);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."CONSIGNEE" IS '收货人';

-- 4）通知人 NOTIFY PARTY（长度 300 × 2 = 600）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "NOTIFY_PARTY" VARCHAR(600);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."NOTIFY_PARTY" IS '通知人';

-- 5）仓储地址（长度 300 × 2 = 600）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "WAREHOUSE_ADDRESS" VARCHAR(600);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."WAREHOUSE_ADDRESS" IS '仓储地址';

-- 6）联系人（长度 20 × 2 = 40）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "CONTACT_PERSON" VARCHAR(40);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."CONTACT_PERSON" IS '联系人';

-- 7）联系电话（长度 20 × 2 = 40）
ALTER TABLE "BIZ_TOBACOO"."T_BIZ_MERCHANT" ADD if not EXISTS "CONTACT_PHONE" VARCHAR(40);
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_MERCHANT"."CONTACT_PHONE" IS '联系电话';










--changeset hrfan1:
CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"
(
    "ID" VARCHAR(80) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(120),
    "DATA_STATE" VARCHAR(20),
    "VERSION_NO" VARCHAR(20),
    "TRADE_CODE" VARCHAR(20),
    "SYS_ORG_CODE" VARCHAR(20),
    "PARENT_ID" VARCHAR(80),
    "CREATE_BY" VARCHAR(100) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    "PERMIT_NUMBER" VARCHAR(200),
    "ARRIVAL_DATE" TIMESTAMP(6),
    "ENTRY_NO" VARCHAR(36),
    "ENTRY_DATE" TIMESTAMP(6),
    "RELEASE_DATE" TIMESTAMP(6),
    "NOTE" VARCHAR(400),
    "HEAD_ID" VARCHAR(100),
    CONSTRAINT "PK_T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT_SID" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR) ;
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT" IS '进货管理-';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."ID" IS '主键ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."DATA_STATE" IS '数据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."VERSION_NO" IS '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."TRADE_CODE" IS '企业10位编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."SYS_ORG_CODE" IS '组织机构代码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."PARENT_ID" IS '父级ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."INSERT_USER_NAME" IS '插入用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."UPDATE_USER_NAME" IS '更新用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."EXTEND10" IS '扩展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."PERMIT_NUMBER" IS '准运证编号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."ARRIVAL_DATE" IS '到货日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."ENTRY_NO" IS '报关单号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."ENTRY_DATE" IS '申报日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."RELEASE_DATE" IS '放行日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_DOCUMENT"."HEAD_ID" IS '进货单表头ID';



CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"
(
    "ID" VARCHAR(40) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(120),
    "DATA_STATE" VARCHAR(20),
    "VERSION_NO" VARCHAR(20),
    "TRADE_CODE" VARCHAR(20),
    "SYS_ORG_CODE" VARCHAR(20),
    "PARENT_ID" VARCHAR(80),
    "CREATE_BY" VARCHAR(100) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    "CONTRACT_NO" VARCHAR(120),
    "PURCHASE_NO" VARCHAR(120),
    "CUSTOMER" VARCHAR(400),
    "SUPPLIER" VARCHAR(400),
    "INVOICE_NO" VARCHAR(120),
    "PORT_OF_DEPARTURE" VARCHAR(100),
    "DESTINATION" VARCHAR(100),
    "PAYMENT_METHOD" VARCHAR(40),
    "PRICE_TERM" VARCHAR(20),
    "PRICE_TERM_PORT" VARCHAR(40),
    "VESSEL_VOYAGE" VARCHAR(200),
    "SAILING_DATE" TIMESTAMP(6),
    "EXPECTED_ARRIVAL_DATE" TIMESTAMP(6),
    "SALES_DATE" TIMESTAMP(6),
    "CONTRACT_AMOUNT" NUMERIC(19,4),
    "INSURANCE_RATE" NUMERIC(19,4),
    "INSURANCE_MARKUP" NUMERIC(19,4),
    "DOCUMENT_CREATOR" VARCHAR(20),
    "DOCUMENT_DATE" TIMESTAMP(6),
    "DOCUMENT_STATUS" VARCHAR(20),
    "CONFIRM_TIME" TIMESTAMP(6),
    "APPROVAL_STATUS" VARCHAR(20),
    "DATE_OF_CONTRACT" TIMESTAMP(6),
    "IS_NEXT" VARCHAR(1) DEFAULT '0',
    "PURCHASE_CONTRACT_NO" VARCHAR(120),
    "ENTRY_NO" VARCHAR(200),
    "ENTRY_DATE" TIMESTAMP(6),
    "SERIAL_NO" INTEGER,
    "SALES_DOCUMENT_STATUS" VARCHAR(50),
    "SELL_CONTRACT_NO" VARCHAR(120),
    "CANCEL_DATE" TIMESTAMP(6),
    "NOTE" VARCHAR(400),
    "CONTRACT_HEAD_ID" VARCHAR(80),
    CONSTRAINT "PK_T_BIZ_SMOKE_MACHINE_INCOMING__GOODS_HEAD" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR) ;
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD" IS '（3）烟机设备-进货单-表头数据';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."ID" IS '主键ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."DATA_STATE" IS '数据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."VERSION_NO" IS '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."TRADE_CODE" IS '企业10位编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."SYS_ORG_CODE" IS '组织机构代码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."PARENT_ID" IS '父级ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."INSERT_USER_NAME" IS '插入用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."UPDATE_USER_NAME" IS '更新用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXTEND10" IS '扩展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."CONTRACT_NO" IS '合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."PURCHASE_NO" IS '进货单号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."CUSTOMER" IS '客户';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."SUPPLIER" IS '供应商';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."INVOICE_NO" IS '发票号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."PORT_OF_DEPARTURE" IS '启运港';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."DESTINATION" IS '目的地/港';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."PAYMENT_METHOD" IS '付款方式';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."PRICE_TERM" IS '价格条款';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."PRICE_TERM_PORT" IS '价格条款对应港口';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."VESSEL_VOYAGE" IS '船名航次';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."SAILING_DATE" IS '开航日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."EXPECTED_ARRIVAL_DATE" IS '预计到达日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."SALES_DATE" IS '做销日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."CONTRACT_AMOUNT" IS '合同金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."INSURANCE_RATE" IS '保险费率%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."INSURANCE_MARKUP" IS '投保加成%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."DOCUMENT_CREATOR" IS '制单人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."DOCUMENT_DATE" IS '制单日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."DOCUMENT_STATUS" IS '单据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."CONFIRM_TIME" IS '确认时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."APPROVAL_STATUS" IS '审批状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."DATE_OF_CONTRACT" IS '签约日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."IS_NEXT" IS '是否流入下一个节点';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."PURCHASE_CONTRACT_NO" IS '购销合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."ENTRY_NO" IS '报关单号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."ENTRY_DATE" IS '报关日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."SERIAL_NO" IS '序号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."SALES_DOCUMENT_STATUS" IS '销售状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."SELL_CONTRACT_NO" IS '购销合同号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."CANCEL_DATE" IS '作销日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_HEAD"."CONTRACT_HEAD_ID" IS '外商合同表头SID';



CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"
(
    "ID" VARCHAR(80) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(120),
    "DATA_STATE" VARCHAR(20),
    "VERSION_NO" VARCHAR(20),
    "TRADE_CODE" VARCHAR(20),
    "SYS_ORG_CODE" VARCHAR(20),
    "PARENT_ID" VARCHAR(80),
    "CREATE_BY" VARCHAR(100) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    "GOODS_NAME" VARCHAR(160),
    "NOTE" VARCHAR(500),
    "PRODUCT_MODEL" VARCHAR(200),
    "QUANTITY" NUMERIC(19,4),
    "UNIT" VARCHAR(60),
    "UNIT_PRICE" NUMERIC(19,8),
    "AMOUNT" NUMERIC(19,4),
    "DELIVERY_DATE" TIMESTAMP(6),
    "TOTAL_USD" NUMERIC(19,4),
    "REMARKS" VARCHAR(400),
    "HEAD_ID" VARCHAR(40),
    "IN_QUANTITY" NUMERIC(19,6),
    "IN_UNIT" VARCHAR(60),
    "CURR" VARCHAR(40),
    "INVOICE_NO" VARCHAR(60),
    "CONTRACT_LIST_ID" VARCHAR(80),
    CONSTRAINT "PK_T_BIZT_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST_SID" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR) ;
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST" IS '进过管理-表体列表';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."ID" IS '主键ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."DATA_STATE" IS '数据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."VERSION_NO" IS '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."TRADE_CODE" IS '企业10位编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."SYS_ORG_CODE" IS '组织机构代码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."PARENT_ID" IS '父级ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."INSERT_USER_NAME" IS '插入用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."UPDATE_USER_NAME" IS '更新用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."EXTEND10" IS '扩展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."GOODS_NAME" IS '商品名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."NOTE" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."PRODUCT_MODEL" IS '规格';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."QUANTITY" IS '数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."UNIT" IS '单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."UNIT_PRICE" IS '单价';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."AMOUNT" IS '金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."DELIVERY_DATE" IS '交货日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."TOTAL_USD" IS '总价折美元';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."REMARKS" IS '备注';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."HEAD_ID" IS '表头HEAD_ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."IN_QUANTITY" IS '进口数量';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."IN_UNIT" IS '进口单位';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."CURR" IS '币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."INVOICE_NO" IS '进口发票号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_LIST"."CONTRACT_LIST_ID" IS '合同表体的ID';




CREATE TABLE IF NOT EXISTS "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"
(
    "ID" VARCHAR(80) DEFAULT SYS_GUID() NOT NULL,
    "BUSINESS_TYPE" VARCHAR(120),
    "DATA_STATE" VARCHAR(20),
    "VERSION_NO" VARCHAR(20),
    "TRADE_CODE" VARCHAR(20),
    "SYS_ORG_CODE" VARCHAR(20),
    "PARENT_ID" VARCHAR(80),
    "CREATE_BY" VARCHAR(100) NOT NULL,
    "CREATE_TIME" TIMESTAMP(6) DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "UPDATE_BY" VARCHAR(100),
    "UPDATE_TIME" TIMESTAMP(6),
    "INSERT_USER_NAME" VARCHAR(100),
    "UPDATE_USER_NAME" VARCHAR(100),
    "EXTEND1" VARCHAR(400),
    "EXTEND2" VARCHAR(400),
    "EXTEND3" VARCHAR(400),
    "EXTEND4" VARCHAR(400),
    "EXTEND5" VARCHAR(400),
    "EXTEND6" VARCHAR(400),
    "EXTEND7" VARCHAR(400),
    "EXTEND8" VARCHAR(400),
    "EXTEND9" VARCHAR(400),
    "EXTEND10" VARCHAR(400),
    "HEAD_ID" VARCHAR(100),
    "CODE_NO" VARCHAR(120),
    "INSURANCE_COMPANY" VARCHAR(400),
    "INSURED_PERSON" VARCHAR(400),
    "INVOICE_TITLE" VARCHAR(400),
    "TRANSPORT_NAME" VARCHAR(400),
    "DEPARTURE_DATE" TIMESTAMP(6),
    "ROUTE_FROM" VARCHAR(400),
    "ROUTE_VIA" VARCHAR(400),
    "ROUTE_TO" VARCHAR(400),
    "INSURANCE_TYPE" VARCHAR(400),
    "CURRENCY" VARCHAR(20),
    "INSURANCE_PREMIUM_RATE" NUMBER(19,4),
    "INSURANCE_AMOUNT" NUMBER(19,4),
    "INSURANCE_RATE" NUMBER(19,4),
    "PREMIUM" NUMBER(19,2),
    "INSURANCE_DATE" TIMESTAMP(6),
    "REMARK" VARCHAR(1000),
    CONSTRAINT "PK_T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB_SID" NOT CLUSTER PRIMARY KEY("ID")) STORAGE(ON "BIZ_TOBACOO", CLUSTERBTR) ;
COMMENT ON TABLE "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB" IS '进货单-投保信息';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."ID" IS '主键ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."BUSINESS_TYPE" IS '业务类型';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."DATA_STATE" IS '数据状态';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."VERSION_NO" IS '版本号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."TRADE_CODE" IS '企业10位编码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."SYS_ORG_CODE" IS '组织机构代码';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."PARENT_ID" IS '父级ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."CREATE_BY" IS '创建人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."CREATE_TIME" IS '创建时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."UPDATE_BY" IS '更新人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."UPDATE_TIME" IS '更新时间';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."INSERT_USER_NAME" IS '插入用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."UPDATE_USER_NAME" IS '更新用户名';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND1" IS '扩展字段1';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND2" IS '扩展字段2';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND3" IS '扩展字段3';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND4" IS '扩展字段4';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND5" IS '扩展字段5';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND6" IS '扩展字段6';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND7" IS '扩展字段7';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND8" IS '扩展字段8';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND9" IS '扩展字段9';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."EXTEND10" IS '扩展字段10';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."HEAD_ID" IS '表头ID';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."CODE_NO" IS '编号';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."INSURANCE_COMPANY" IS '保险公司';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."INSURED_PERSON" IS '被保险人';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."INVOICE_TITLE" IS '发票抬头';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."TRANSPORT_NAME" IS '运输工具名称';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."DEPARTURE_DATE" IS '开航日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."ROUTE_FROM" IS '运输路线自';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."ROUTE_VIA" IS '经';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."ROUTE_TO" IS '至';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."INSURANCE_TYPE" IS '投保险别';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."CURRENCY" IS '币种';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."INSURANCE_PREMIUM_RATE" IS '投保加成%';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."INSURANCE_AMOUNT" IS '保险金额';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."INSURANCE_RATE" IS '保险费率';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."PREMIUM" IS '保费';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."INSURANCE_DATE" IS '投保日期';
COMMENT ON COLUMN "BIZ_TOBACOO"."T_BIZ_SMOKE_MACHINE_INCOMING_GOODS_TB"."REMARK" IS '备注';

