package com.dcjet.cs.baseInfoCustomerParams.service;
import com.dcjet.cs.baseInfoCustomerParams.dao.BaseInfoCustomerParamsMapper;
import com.dcjet.cs.baseInfoCustomerParams.mapper.BaseInfoCustomerParamsDtoMapper;
import com.dcjet.cs.baseInfoCustomerParams.model.BaseInfoCustomerParams;

import com.dcjet.cs.bi.model.BiCustomerParams;
import com.dcjet.cs.dto.baseInfoCustomerParams.BaseInfoCustomerParamsDto;
import com.dcjet.cs.dto.baseInfoCustomerParams.BaseInfoCustomerParamsParam;
import com.dcjet.cs.dto.bi.BiCustomerParamsDto;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.xdo.common.base.service.BaseService;
import com.xdo.common.exception.ErrorException;
import com.xdo.common.token.UserInfoToken;
import com.xdo.domain.PageParam;
import com.xdo.domain.ResultObject;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import tk.mybatis.mapper.common.Mapper;
import xdoi18n.XdoI18nUtil;

import javax.annotation.Resource;
import java.beans.Transient;
import java.util.*;
import java.util.stream.Collectors;

/**
 * generated by Generate 神码
 * Service;
 *
 * <AUTHOR>
 * @date: 2019-6-6
 */
@Service
public class BaseInfoCustomerParamsService extends BaseService<BaseInfoCustomerParams> {
    @Resource
    private BaseInfoCustomerParamsMapper mapper;

    @Resource
    private BaseInfoCustomerParamsDtoMapper dtoMapper;

    @Override
    public Mapper<BaseInfoCustomerParams> getMapper() {
        return mapper;
    }
    /**
     * 获取分页信息
     *
     * <AUTHOR>
     * @param params
     * @param pageParam
     * @return
     */
    public ResultObject<List<BaseInfoCustomerParamsDto>> getListPaged(BaseInfoCustomerParamsParam params, PageParam pageParam, UserInfoToken userInfo) {
        BaseInfoCustomerParams baseInfoCustomerParams = dtoMapper.toPo(params);

        // 启用分页查询
        baseInfoCustomerParams.setTradeCode(userInfo.getCompany());
        Page<BaseInfoCustomerParams> page = PageHelper.startPage(pageParam.getPage(), pageParam.getLimit(), pageParam.getSortOrderContent())
                .doSelectPage(() -> mapper.getList(baseInfoCustomerParams));

        List<BaseInfoCustomerParamsDto> dtos = page.getResult().stream().map(head -> {
            BaseInfoCustomerParamsDto dto = dtoMapper.toDto(head);
            if (StringUtils.isNotEmpty(dto.getBusinessType())){
                dto.setBusinessTypeList(Arrays.asList(dto.getBusinessType().split(",")));
            }else {
                dto.setBusinessTypeList(new ArrayList<>());
            }
            return dto;
        }).collect(Collectors.toList());

        //
		ResultObject<List<BaseInfoCustomerParamsDto>> paged = ResultObject.createInstance(dtos, (int) page.getTotal(), page.getPageNum());
        return paged;
    }
    /**
     * 功能描述:新增
     *
     * @param params
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<BaseInfoCustomerParamsDto> insert(BaseInfoCustomerParamsParam params, UserInfoToken userInfo) {
        ResultObject<BaseInfoCustomerParamsDto> resultObject = ResultObject.createInstance(true, "新增成功");
        BaseInfoCustomerParams baseInfoCustomerParams = dtoMapper.toPo(params);

        /**
         * 规范固定字段
         */
        String sid = UUID.randomUUID().toString();
        baseInfoCustomerParams.setSid(sid);
        baseInfoCustomerParams.setTradeCode(userInfo.getCompany());
        baseInfoCustomerParams.setInsertUser(userInfo.getUserNo());
        baseInfoCustomerParams.setInsertUserName(userInfo.getUserName());
        baseInfoCustomerParams.setInsertTime(new Date());
        checkData(baseInfoCustomerParams,userInfo);
        if (CollectionUtils.isNotEmpty(baseInfoCustomerParams.getBusinessTypeList())){
            baseInfoCustomerParams.setBusinessType(String.join(",", baseInfoCustomerParams.getBusinessTypeList()));
        }else {
            baseInfoCustomerParams.setBusinessType(null);
        }

        // 新增数据
        int insertStatus = mapper.insert(baseInfoCustomerParams);
        BaseInfoCustomerParamsDto dto = insertStatus > 0 ? dtoMapper.toDto(baseInfoCustomerParams) : null;
        if (dto != null) {
            resultObject.setData(dto);
        } else {
            throw new ErrorException(400, ResultObject.INSERT_FAIL);
        }
        return resultObject;
    }
    /**
     * 功能描述:修改
     *
     * @param params
     * @param userInfo
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ResultObject<BaseInfoCustomerParamsDto> update(BaseInfoCustomerParamsParam params, UserInfoToken userInfo) {
        ResultObject<BaseInfoCustomerParamsDto> resultObject = ResultObject.createInstance(true, "");
        BaseInfoCustomerParams baseInfoCustomerParams = mapper.selectByPrimaryKey(params.getSid());
        dtoMapper.updatePo(params, baseInfoCustomerParams);

        baseInfoCustomerParams.setUpdateUser(userInfo.getUserNo());
        baseInfoCustomerParams.setUpdateUserName(userInfo.getUserName());
        baseInfoCustomerParams.setUpdateTime(new Date());
        if (CollectionUtils.isNotEmpty(baseInfoCustomerParams.getBusinessTypeList())){
            baseInfoCustomerParams.setBusinessTypeList( baseInfoCustomerParams.getBusinessTypeList().stream()
                    .filter(element -> element != null)
                    .collect(Collectors.toList()));

            baseInfoCustomerParams.setBusinessType(String.join(",", baseInfoCustomerParams.getBusinessTypeList()));
        }else {
            throw new ErrorException(400, XdoI18nUtil.t("常用标志(业务类型)不能为空！"));
            //baseInfoCustomerParams.setBusinessType(null);
        }
        checkData(baseInfoCustomerParams,userInfo);
        // 更新数据
        int update = mapper.updateByPrimaryKey(baseInfoCustomerParams);

        BaseInfoCustomerParamsDto dto = update > 0 ? dtoMapper.toDto(baseInfoCustomerParams) : null;
        if (dto != null) {
            resultObject.setData(dto);
        } else {
            throw new ErrorException(400, ResultObject.UPDATE_FAIL);
        }
        return resultObject;
    }

    /**
     * 功能描述:批量删除
     *
     * @param sids
     * @return
     */
    @Transient
    public void delete(List<String> sids) {
        mapper.deleteBySids(sids);
    }

    private void checkData(BaseInfoCustomerParams params, UserInfoToken userInfo) {
        if ("CURR".equals(params.getParamsType())) {
            if (StringUtils.isBlank(params.getParamsCode())) {
                throw new ErrorException(400, "币制代码不能为空");
            }
        }

        String errorMessage = "";

        Map<String, Object> map = new HashMap<>(4);
        map.put("sid", params.getSid());
        map.put("tradeCode", userInfo.getCompany());
        map.put("paramsCode", params.getParamsCode());
        map.put("paramsType", params.getParamsType());
        if (getParamsCode(map)) {
            if ("CURR".equals(params.getParamsType())) {
                errorMessage = "币制代码已存在";
            }
            if (StringUtils.isNotBlank(errorMessage)) {
                throw new ErrorException(400, errorMessage);
            }
        }
    }

    public boolean getParamsCode(Map<String, Object> param) {
        int sum = mapper.getParamsCode(param);
        return sum > 0;
    }

    public String getNameByCode(String paramsCode, String paramsType, String businessType) {
        return mapper.getNameByCode(paramsCode,paramsType,businessType);
    }
}
