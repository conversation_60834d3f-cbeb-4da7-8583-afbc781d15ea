package com.dcjet.cs.dto.customerAccount;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import org.springframework.format.annotation.DateTimeFormat;
/**
 * 
 * <AUTHOR>
 * @date: 2025-7-2
 */
@ApiModel(value = "返回信息")
@Setter @Getter
public class BizCustomerAccountTobacooDto implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
      * 主键
      */
    @ApiModelProperty("主键")
	private  String sid;
	/**
      * 创建人
      */
    @ApiModelProperty("创建人")
	private  String createBy;
	/**
      * 创建时间
      */
    @ApiModelProperty("创建时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date createTime;
	/**
      * 创建人名称
      */
    @ApiModelProperty("创建人名称")
	private  String createUserName;
	/**
      * 更新人
      */
    @ApiModelProperty("更新人")
	private  String updateBy;
	/**
      * 最后修改时间
      */
    @ApiModelProperty("最后修改时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date updateTime;
	/**
      * 最后修改人名称
      */
    @ApiModelProperty("最后修改人名称")
	private  String updateUserName;
	/**
      * 企业编码
      */
    @ApiModelProperty("企业编码")
	private  String tradeCode;
	/**
      * 创建人部门编码
      */
    @ApiModelProperty("创建人部门编码")
	private  String sysOrgCode;
	/**
      * 拓展字段1
      */
    @ApiModelProperty("拓展字段1")
	private  String extend1;
	/**
      * 拓展字段2
      */
    @ApiModelProperty("拓展字段2")
	private  String extend2;
	/**
      * 拓展字段3
      */
    @ApiModelProperty("拓展字段3")
	private  String extend3;
	/**
      * 拓展字段4
      */
    @ApiModelProperty("拓展字段4")
	private  String extend4;
	/**
      * 拓展字段5
      */
    @ApiModelProperty("拓展字段5")
	private  String extend5;
	/**
      * 拓展字段6
      */
    @ApiModelProperty("拓展字段6")
	private  String extend6;
	/**
      * 拓展字段7
      */
    @ApiModelProperty("拓展字段7")
	private  String extend7;
	/**
      * 拓展字段8
      */
    @ApiModelProperty("拓展字段8")
	private  String extend8;
	/**
      * 拓展字段9
      */
    @ApiModelProperty("拓展字段9")
	private  String extend9;
	/**
      * 拓展字段10
      */
    @ApiModelProperty("拓展字段10")
	private  String extend10;
	/**
      * 业务类型
      */
    @ApiModelProperty("业务类型")
	private  String businessType;
	/**
      * 结算单号
      */
    @ApiModelProperty("结算单号")
	private  String accountNo;
	/**
      * 合同号
      */
    @ApiModelProperty("合同号")
	private  String contractNo;
	/**
      * 币种
      */
    @ApiModelProperty("币种")
	private  String curr;
	/**
      * 汇率
      */
    @ApiModelProperty("汇率")
	private  BigDecimal exchangeRate;
	/**
      * 货款
      */
    @ApiModelProperty("货款")
	private  BigDecimal goodsPrice;
	/**
      * 代理费率%
      */
    @ApiModelProperty("代理费率%")
	private  BigDecimal agentFeeRate;
	/**
      * 代理费（不含税）
      */
    @ApiModelProperty("代理费（不含税）")
	private  BigDecimal agentFee;
	/**
      * 代理费税额
      */
    @ApiModelProperty("代理费税额")
	private  BigDecimal agentTaxFee;
	/**
      * 代理费（价税合计）
      */
    @ApiModelProperty("代理费（价税合计）")
	private  BigDecimal agentFeeTotal;
	/**
      * 结算日期
      */
    @ApiModelProperty("结算日期")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date businessDate;
	/**
      * 商品名称
      */
    @ApiModelProperty("商品名称")
	@JsonProperty("gName")
	private  String gName;
	/**
      * 发送财务系统
      */
    @ApiModelProperty("发送财务系统")
	private  String sendFinance;
	/**
      * 商品与数量
      */
    @ApiModelProperty("商品与数量")
	private  String producrSome;
	/**
      * 备注
      */
    @ApiModelProperty("备注")
	private  String note;
	/**
      * 是否红冲
      */
    @ApiModelProperty("是否红冲")
	private  String redFlush;
	/**
      * 单据状态
      */
    @ApiModelProperty("单据状态")
	private  String status;
	/**
      * 审核状态
      */
    @ApiModelProperty("审核状态")
	private  String apprStatus;
	/**
      * 确认时间
      */
    @ApiModelProperty("确认时间")
	@DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
	private  Date confirmTime;
	/**
      * 是否确认
      */
    @ApiModelProperty("是否确认")
	private  String isConfirm;
	/**
      * 外商合同、进货明细数据标记
      */
    @ApiModelProperty("外商合同、进货明细数据标记")
	private  String purchaseMark;
	/**
      * 外商合同、进货明细数据标记
      */
    @ApiModelProperty("外商合同、进货明细数据标记")
	private  String purchaseNoMark;
	/**
      * 人民币货款
      */
    @ApiModelProperty("人民币货款")
	private  BigDecimal goodsPriceRmb;
	/**
      * 增值税率%
      */
    @ApiModelProperty("增值税率%")
	private  BigDecimal vatRate;
	/**
      * 货代费
      */
    @ApiModelProperty("货代费")
	private  BigDecimal freightForwardingFee;
	/**
      * 保险费
      */
    @ApiModelProperty("保险费")
	private  BigDecimal insuranceFee;
	/**
      * 已结算款项合计
      */
    @ApiModelProperty("已结算款项合计")
	private  BigDecimal costFee;
	/**
      * 实际预收款
      */
    @ApiModelProperty("实际预收款")
	private  BigDecimal depositReceived;
	/**
      * 应退款项
      */
    @ApiModelProperty("应退款项")
	private  BigDecimal refundFee;
	/**
      * 客户
      */
    @ApiModelProperty("客户")
	private  String customer;

	private String createrBy;
	private String createrUserName;
	private Date createrTime;
	private  String freightRatio;
	private  String businessLocation;
	private  BigDecimal totalAgentFeeRate;
	private  BigDecimal totalAgentFee;
	private  BigDecimal totalAgentTaxFee;
	private  BigDecimal totalAgentFeeTotal;
	private  BigDecimal totalAmount;
}
