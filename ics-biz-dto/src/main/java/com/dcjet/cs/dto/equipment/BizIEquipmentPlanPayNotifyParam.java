package com.dcjet.cs.dto.equipment;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 * <AUTHOR>
 * @date: 2025-7-8
 */
@Setter
@Getter
@ApiModel(value = "传入参数")
public class BizIEquipmentPlanPayNotifyParam implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
    * 主键
    */
    @ApiModelProperty("主键")
    private String id;
	/**
    * 关联字段
    */
	@ApiModelProperty("表头关联字段")
	@NotEmpty(message="表头关联字段不能为空！")
	private String headId;
	/**
    * 序号
    */
	@ApiModelProperty("序号")
	private String serialNo;
	/**
    * 款项类型（多复选框）
    */
	@NotEmpty(message="款项类型不能为空！")
	@ApiModelProperty("款项类型")
	private  String paymentType;
	/**
    * 合同金额
    */
	@NotNull(message="合同金额不能为空！")
	@Digits(integer = 13, fraction = 6, message = "合同金额必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("合同金额")
	private  BigDecimal contractAmount;
	/**
    * 汇率
    */
	@NotNull(message="汇率不能为空！")
	@Digits(integer = 13, fraction = 6, message = "汇率必须为数字,整数位最大13位,小数最大6位!")
	@ApiModelProperty("汇率")
	private  BigDecimal exchangeRate;
	/**
    * 进出口公司代理费率%
    */
	@NotNull(message="进出口公司代理费率%不能为空！")
	@Digits(integer = 15, fraction = 4, message = "进出口公司代理费率%必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("进出口公司代理费率%")
	private  BigDecimal importExportAgentRate;
	/**
    * 进出口公司代理费
    */
	@NotNull(message="进出口公司代理费不能为空！")
	@Digits(integer = 17, fraction = 2, message = "进出口公司代理费必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("进出口公司代理费")
	private  BigDecimal importExportAgentFee;
	/**
    * 总公司代理费率%
    */
	@NotNull(message="总公司代理费率%不能为空！")
	@Digits(integer = 15, fraction = 4, message = "总公司代理费率%必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("总公司代理费率%")
	private  BigDecimal headOfficeAgentRate;
	/**
    * 总公司代理费
    */
	@NotNull(message="总公司代理费不能为空！")
	@Digits(integer = 17, fraction = 2, message = "总公司代理费必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("总公司代理费")
	private  BigDecimal headOfficeAgentFee;
	/**
    * 计费箱数
    */
	@NotNull(message="计费箱数不能为空！")
	@Digits(integer = 15, fraction = 4, message = "计费箱数必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("计费箱数")
	private  String chargeContainerCount;
	/**
    * 通关费
    */
	@Digits(integer = 15, fraction = 4, message = "通关费必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("通关费")
	private  BigDecimal customsClearanceFee;
	/**
    * 验柜服务费
    */
	@Digits(integer = 15, fraction = 4, message = "验柜服务费必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("验柜服务费")
	private  BigDecimal containerInspectionFee;
	/**
    * 货代费用
    */
	@NotNull(message="货代费用不能为空！")
	@Digits(integer = 17, fraction = 2, message = "货代费用必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("货代费用")
	private  BigDecimal freightForwardingFee;
	/**
    * 保险费率
    */
	@Digits(integer = 15, fraction = 4, message = "保险费率必须为数字,整数位最大15位,小数最大4位!")
	@ApiModelProperty("保险费率")
	private  BigDecimal insuranceRate;
	/**
    * 保险费
    */
	@Digits(integer = 17, fraction = 2, message = "保险费必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("保险费")
	private  BigDecimal insuranceFee;
	/**
    * 划款金额（RMB）
    */
	@NotNull(message="划款金额不能为空！")
	@Digits(integer = 17, fraction = 2, message = "划款金额（RMB）必须为数字,整数位最大17位,小数最大2位!")
	@ApiModelProperty("划款金额（RMB）")
	private  BigDecimal remittanceAmountRmb;
	/**
    * 备注
    */
	@XdoSize(max = 200, message = "备注长度不能超过200位字节长度(一个汉字2位字节长度)!")
	@ApiModelProperty("备注")
	private  String remark;
}
