package com.dcjet.cs.dto.equipment;
import com.fasterxml.jackson.annotation.JsonProperty;
import javax.validation.constraints.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import java.util.Date;
import java.math.BigDecimal;
import lombok.Getter;
import lombok.Setter;
import com.xdo.validation.annotation.XdoSize;
/**
 * <AUTHOR>
 * @date: 2025-7-8
 */
@Setter
@Getter
@ApiModel(value = "传入参数")
public class BizIEquipmentPlanListParam implements Serializable {
    private static final long serialVersionUID = 1L;
	/**
    * 主键
    */
    @ApiModelProperty("主键")
    private String sid;
	/**
    * 关联字段
    */
	@ApiModelProperty("表头关联字段")
	@NotEmpty(message="表头关联字段不能为空！")
	private String headId;
	/**
    * 预计交货日期
    */
	@ApiModelProperty("预计交货日期")
	private  Date estDeliveryDate;
}
